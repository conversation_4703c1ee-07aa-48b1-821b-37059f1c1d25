import React from "react";

export default function Home() {
  return (
    <>
      <div className="prose">
        <section>
          <h2 id="innledning">Innledning</h2>
          <p>
            Denne brukerveiledningen er laget for eksamensvakter som skal
            benytte PGS-monitor i PGS. Merk at brukerstøtte går
            <a
              href="https://www.udir.no/om-udir/tjenestevei-skole-barnehage/"
              className="ml-1 mr-1"
            >
              {" "}
              tjenestevei
            </a>
            . For eksempel skal eksamensvakter kontakte sin skoleleder eller
            eksamensansvarlige på skolen.
          </p>
          <p>
            PGS-monitor i PGS er tilgjengelig for de som er eksamensvakter for
            PGS, dvs. at de som har rolletilgangen
            <i className="ml-1 mr-1">eksamensvakt</i>
            for PGS. PGS-monitor er også tilgjengelig for alle som er
            skoleadministrator i PAS-eksamen samt de som representerer skoleeier
            og statsforvalteren i PAS. Tilganger til PGS tildeles av
            skoleadministrator i PAS. Dersom du er usikker på hvilke tilganger
            du har, kan du får oversikt over dine tildelte rolletilganger ved å
            gå til{" "}
            <a className="ml-1" href="https://uidp-admin.udir.no/min-konto">
              minkonto.udir.no
            </a>
            .
          </p>
          <p>
            En forutsetning for at eksamensvakten skal kunne benytte seg av
            PGS-monitor er at skoleadministrator også har gitt eksamensvakten
            tilgang til aktuelle kandidatgrupper i PAS.
          </p>
          <p>
            PGS-monitor er også tilgjengelig for alle som er
            <i className="mx-1">skoleadministrator</i> i PAS samt de som
            representerer skoleeier og statsforvalteren i PAS. Dvs. brukere som
            har en av følgende roller i PAS-eksamen: Skoleadministrator,
            Skoleadministrator+, Kommune, Fylkeskommune, Vigo
            eksamensadministrator og Statsforvalter.
          </p>
          <p>
            De fleste eksamenene vil for V-2025 skje i EPS, det heldigitale
            gjennomføringssystemet. Majoriteten av kandidatene skal gjennomføre
            eksamen i EPS. Hvis du er vakt for noen av disse, vil du i
            <a className="ml-1" href="https://eksamen.udir.no/Sysadmin/Skoler">
              PAS-eksamen
            </a>{" "}
            se en lenke til «monitorering av eksamener etter ny læreplan». Du
            finner veiledning for EPS-monitoren i PAS, i monitorbildet. I{" "}
            <a href="https://eksamensplan.udir.no">eksamensplanen</a> ser du
            hvilket gjennomføringssystem som gjelder for de ulike fagkodene. Du
            finner en egen oversikt over fagkoder i PGS for videregående skole
            ved å gå til{" "}
            <a href="/fagkoderVGS">
              PGS-admin/fagkoderVGS.
            </a>
          </p>
          <p>Merk at alle todelte eksamener skjer i PGS.</p>
          <p>
            Det er flere brukerveiledninger for PGS. Disse finner du på siden
            for{" "}
            <a href="/brukerveiledning">
              brukerveiledning
            </a>{" "}
            i PGS-admin. Alle persondata som vises i skjermbilder i våre
            veiledninger er fiktive data.
          </p>
          <p>
            På udir.no finner du praktisk informasjon om organisering og
            gjennomføring av sentralt gitt skriftlig eksamen:{" "}
            <a href="https://www.udir.no/eksamen-og-prover/eksamen/">
              https://www.udir.no/eksamen-og-prover/eksamen/
            </a>
          </p>
        </section>
        <section>
          <h2 id="hurtigguide">Hurtigguide</h2>

          <ul className="list-decimal">
            <li>
              Naviger til
              <a className="ml-1" href="/pgs-monitor">
                https://pgs-admin.udir.no/pgs-monitor
              </a>
            </li>

            <li>
              Når kandidatene er innlogget i PGS og kommer til
              autoriseringssiden vil knappen "Gi tilgang" automatisk dukke opp i
              PGS-monitor.
            </li>
            <li>
              Når du trykker på "Gi tilgang" vil kandidaten få tilgang til PGS
              og automatisk sendes videre til oppgavesiden eller ventesiden
              (avhengig om eksamen har startet eller ikke).
            </li>
            <li>
              Dersom du skal levere en fil for en kandidat klikker du på "Flere
              valg" og deretter "Lever for kandidat".
            </li>
          </ul>
        </section>
        <section>
          <h2 id="naviger-til-pgs-monitor">Naviger til PGS-monitor </h2>
          <p>
            Logg inn som eksamensvakt eller skoleadministrator i PAS-eksamen på
            <a className="ml-1 " href="https://eksamen.udir.no">
              eksamen.udir.no
            </a>
            . Gå til «Monitor» og «Gå til monitorering av PGS eksamener». Du
            kommer deretter til administrasjonsmodulen i PGS. Her velger du
            funksjonen “ PGS-monitor”.
          </p>
          <p>
            Alternativt kan du logg inn i
            <a className="ml-1 mr-1" href="https://pgs-admin.udir.no">
              pgs-admin
            </a>
            og velge PGS-monitor i menyen.
          </p>
          <p>Under ser du et skjermbilde av PGS-monitor.</p>
          <img
            src="/images/kandidatmonitor-hovedside.png"
            alt="kandidatmonitor"
          />
        </section>
        <section>
          <h2 id="finn-kandidatene-dine">Finn kandidatene dine</h2>
          <p>
            I PGS-monitor får du oversikt over kandidatene for din skole basert
            på de kandidatgruppene som du har tilgang på. Det er
            skoleadministrator som i PAS har valgt hvilke kandidatgrupper hver
            enkelt eksamensvakt skal ha tilgang på. Skoleadministrator har
            tilgang på alle kandidatgruppene på skolen.
          </p>
          <p>
            I PGS-monitor vises kandidatene med fullt navn, kandidatnummer,
            kandidatgruppe, fagnavn samt tilgangsstatus og fremdriftsstatus som
            de har i PGS. Fremdriftsstatus vises per eksamensdel. Du kan også
            velge å vise IP-adresse i tabellen ved å aktivere valget "Vis
            IP-adresser". Det er kandidatens sist gjeldende IP-adresse som da
            vises.
          </p>
          <p>
            Du kan søke etter en bestemt kandidat ved å skrive inn
            kandidatnummer eller navn i søkefeltet for kandidat. Du kan også
            filtere på kandidatgruppe, fremdriftsstatus eller tilgangsstatus.
            Når du for en todelt eksamen filtrer på fremdriftsstatus vil du få
            treff både mot del 1 og del 2.
          </p>
          <p>
            Merk at når du filtrer på tilgangsstatus "Venter på tilgang" vil
            alle kandidater som det er mulig å gi tilgang til vises. Dette betyr
            alle med tilgangsstatus "Venter på tilgang" og alle med "Har
            tilgang" som har gjort en ny innlogging.
          </p>
          <p>
            Du kan klikke "Nullstill filter" dersom du ønsker å fjerne alle
            gjeldende filtre.
          </p>
        </section>
        <section>
          <h2 id="oversikt-over-pgs-monitor">Oversikt over PGS-monitor</h2>
          <p>
            I dette kapittelet gis det en kort beskrivelse av funksjonaliteten i
            PGS-monitor. For mye av funksjonaliteten er det egne kapitler senere
            i veiledningen som gir en mer utførlig beskrivelse.
          </p>
          <div>
            <h3 id="tilgang">Tilgang</h3>
            <p>
              I kolonnen for tilgang vises kandidatenes tilgangsstatus.
              Kandidater som har tilgang til PGS har status "Har tilgang". De
              som har status "Ikke tilgang" har ikke fått tilgang ennå, mens de
              som har fått sperret tilgang står med status "Digital tilgang
              sperret". Når kandidaten går til autoriseringssiden vil knappen
              "Gi tilgang" dukke opp i denne kolonnen. Dette er beskrevet
              nærmere i et eget kapittel senere i denne veiledningen.
            </p>
            <p>
              I kolonnen for tilgang vil også fraværstatus vises dersom dette er
              satt for kandidaten. Dvs. at "Ikke-dokumentert fravær" og
              "Dokumentert fravær" vises i denne kolonnen.
            </p>
          </div>
          <div>
            <h3 id="fravaer">Fravær</h3>
            <p>
              I PGS-monitor kan du registrere en kandidat med fravær. Dette er
              beskrevet nærmere i et eget kapittel senere i denne veiledningen.
            </p>
          </div>
          <div>
            <h3 id="sperr-digital-tilgang">Sperr digital tilgang</h3>
            <p>
              Dersom du som eksamensvakt vil forsikre deg om at kandidaten ikke
              får tilgang til PGS, kan du sperre digital tilgang for kandidaten.
              Du går da til "Flere valg" og velger "Sperr digital tilgang".
              Kandidaten vil da få status "Digital tilgang sperret" og være
              blokkert fra å få tilgang til PGS. Dvs. at kandidaten ikke har
              tilgang på eksamensoppgaven og heller ikke har mulighet til å
              levere digitalt. Dersom kandidaten logger seg på vil hen komme til
              ikke-tilgang-siden, se mer om dette i den separate{" "}
              <a href="/brukerveiledning/kandidatgjennomforing">
                brukerveiledningen for kandidatgjennomføring
              </a>
              .
            </p>
            <p>
              Du kan oppheve sperringen ved å gå til "Flere valg" og klikke
              "Opphev sperre".
            </p>
          </div>
          <div>
            <h3 id="fremdriftsstatuser">Fremdriftsstatuser</h3>
            <p>For V-2025 er det kun 4 fremdriftsstatuser:</p>
            <ul>
              <li>Ikke lastet opp</li>
              <li>Laster opp</li>
              <li>Levert digitalt</li>
              <li>Sendes i posten</li>
            </ul>
            <p>
              Det er de samme statusene som gjelder både for del 1 og del 2.
              Fremdriftsstatusen sier noe om hvor i prosessen kandidaten er med
              å levere eksamen. Dersom kandidaten har fått satt en fraværstatus
              vil ikke fremdriftsstatus vises.
            </p>
            <p>
              Fremdriftsstatuser for eksamen med én del vil vises under kolonnen
              "Eksamen". Fremdriftsstatuser for todelt eksamen vil vises hver
              del for seg under kolonnene "Del 1" og "Del 2".
            </p>
            <p>Merk at en kandidatstatus kan endre seg av flere årsaker:</p>
            <ul className="list-disc">
              <li>
                at en kandidat foretar en handling, for eksempel laster opp en
                fil eller leverer besvarelsen sin
              </li>
              <li>
                at en eksamensvakt endrer status i PGS-monitor, leverer for
                kandidaten eller leverer via gruppeopplasteren
              </li>
              <li>
                at skoleadministrator gjør en endring i PAS-eksamen; setter
                fravær eller angir at kandidaten har deltatt
              </li>
            </ul>
            <p>Fremdriftsstatusene er beskrevet nærmere i seksjonene:</p>
            <ul>
              <li>Fremdriftsstatuser – eksamen med én del</li>
              <li>Fremdriftsstatuser – todelt eksamen</li>
            </ul>
          </div>
          <div>
            <h3 id="lever-for-kandidat">Lever for kandidat</h3>
            <p>
              Ved klikke på nedtrekkslisten "Flere valg" og deretter velge
              "Lever for kandidat" kommer du til en egen side for å levere
              besvarelsesfiler på vegne av den valgte kandidaten. Dette er
              beskrevet i detalj i brukerveiledningen for{" "}
              <a href="/brukerveiledning/pgs-monitor/leverFor">
                Lever for kandidat
              </a>
              .
            </p>
          </div>
          <div>
            <h3 id="vis-filer">Vis filer</h3>
            <p>
              Ved klikke på nedtrekkslisten "Flere valg" og deretter velge “Vis
              filer” for en spesifikk kandidat vil du få da opp en modalside som
              viser kandidatens besvarelsesfiler med info om filnavn, størrelse,
              eksamensdel tidspunktet som filen ble lastet opp samt om det var
              kandidaten eller en eksamensvakt som lastet opp filen. Du vil også
              se status på filen, altså om den kun er opplastet eller om den er
              innlevert.
            </p>
            <p>
              Ved å klikke "Last ned filene" laster du ned alle filene for den
              spesifikke kandidaten i en komprimert (zippet) mappe. Dersom du
              ønsker å kun laste ned en spesifikk fil kan du klikke på filnavnet
              til filen. Denne vil ikke være komprimert (zippet) når den lastes
              ned.
            </p>
          </div>
          <div>
            <h3 id="aktivitetslogg">Aktivitetslogg og innloggingssesjon/IP </h3>
            <p>
              Når du under "Flere valg" klikker{" "}
              <span className="underline">Se logg</span> vises aktivitetslogg og
              innloggingssesjon/IP for kandidaten. Dette er beskrevet nærmere i
              et egne kapitler senere i denne veiledningen.
            </p>
          </div>
        </section>
        <section>
          <h2 id="registrer-fravaer">Registrere fravær</h2>

          <p>
            Dersom skolen har mottatt skriftlig dokumentasjon på fravær, settes
            status «Dokumentert fravær». Ellers settes status «Ikke-dokumentert
            fravær» ved fravær. Kandidater som har dokumentert fravær ved
            eksamen, har rett til å gjennomføre første etterfølgende eksamen.
          </p>
          <p>
            Skriftlig eksamen starter kl. 09:00 norsk tid. Elever som møter før
            kl. 10:00, får gjennomføre eksamen, men de får ikke kompensert tapt
            tid. De som møter etter kl. 10:00 får ikke gjennomføre eksamen.
            Altså er kl. 10 et passende tidspunkt å registrere fravær.
          </p>
          <p>
            Hvis du skal registrere en kandidat med "Ikke-dokumentert fravær",
            klikker du på nedtrekkslisten "Flere valg". Deretter klikker du på
            valget «Ikke-dokumentert fravær» i dialogboksen som kommer opp.
            Kandidaten får da status «Ikke-dokumentert fravær». Fremgangsmåten
            er tilsvarende hvis du skal sette «Dokumentert fravær».
          </p>
          <p>
            PGS og PAS-eksamen er integrerte systemer. Skoleadministrator kan
            sette fraværsstatuser i PAS, og disse vil da automatisk overføres
            til PGS. På samme måte vil alle fraværsstatuser som settes i PGS
            automatisk overføres til PAS. Dersom det mot formodning skulle være
            ulik status i PAS og PGS, anbefaler vi at du oppdaterer nettsiden
            for PGS-monitor og oppdaterer nettsiden i PAS.
          </p>
          <p>
            Kandidater som har fått status «Ikke-dokumentert fravær» eller
            «Dokumentert fravær» vil ved pålogging komme direkte til
            fraværssiden i kandidatmodulen. Du kan lese mer om dette i den
            separate{" "}
            <a href="/brukerveiledning/kandidatgjennomforing">
              brukerveiledningen for kandidatgjennomføring
            </a>
            .
          </p>
          <p>
            Dersom det skal endres status for fravær etter eksamensdagen, skal
            det gjøres i PAS-eksamen. Det er mulig å skifte mellom dokumentert
            og ikke-dokumentert fravær etter eksamensdagen i PAS, hvis
            kandidaten leverer dokumentasjon senere.
          </p>
        </section>

        <section>
          <h2 id="gi-tilgang-til-kandidatene">Gi tilgang til kandidatene</h2>
          <p>
            Merk at kandidatene må være innlogget i PGS og stå på
            autoriseringssiden før du kan gi dem tilgang.
          </p>
          <blockquote className="not-italic font-normal">
            <h3 id="infoboks">Infoboks</h3>
            <p className="">
              Med <i>autentisering</i> mener vi her prosessen med å bekrefte en
              identitet. Dette skjer ved at kandidaten logger på med{" "}
              <a className="ml-1 mr-1" href="https://www.feide.no/om-feide">
                Feide
              </a>
              eller kandidatnummer og passord. Autentiseringen foregår i Udir
              sin løsning for identitets- og tilgangskontroll:
              <a
                className="ml-1"
                href="https://uidp-admin.udir.no/info/om-uidp"
                target="_blank"
              >
                UIDP
              </a>
            </p>
            <p>
              Med <i>autorisering</i> mener vi her prosessen med å avgjøre om en
              kandidat skal få tilgang til PGS. Kandidaten autoriseres ved at
              eksamensvakten gir tilgang i PGS-monitor eller at dagspassord
              benyttes.
            </p>
          </blockquote>
          <p>
            Kandidaten autentiserer seg med sin Feide-bruker, eller med
            kandidatnummer og kandidatpassord på innloggingssiden i UIDP.
            Skoleadministrator har tilgang på kandidatnummer og kandidatpassord
            i PAS-eksamen. Der finner man også dagspassordet for de forskjellige
            kandidatgruppene.
          </p>
          <p>
            Se den separate
            <a
              className="ml-1 mr-1"
              href="/brukerveiledning/kandidatgjennomforing"
            >
              brukerveiledningenfor kandidatgjennomføring
            </a>
            i PGS for en nærmere beskrivelse av prosessen som kandidaten går
            igjennom ved innlogging.
          </p>
          <p>
            For at kandidaten skal få tilgang til eksamensoppgaven, og kunne
            levere eksamen digitalt må kandidaten få digital tilgang
            (autoriseres) av en eksamensvakt i PGS-monitor. Merk at kandidaten
            med fordel kan få tilgang til PGS i god tid før eksamen starter.
            Kandidaten får uansett ikke tilgang til eksamensoppgaven i PGS før
            eksamen offisielt starter. Dette gjelder også for todelt eksamen.
          </p>

          <p>
            Når kandidaten har autentisert seg og logget inn i PGS vil hen komme
            til autoriseringssiden i kandidatmodulen. Kandidatens navn vil vises
            i overskriften på siden. Kandidaten har på dette tidspunktet ikke
            fått tilgang til PGS. Autoriseringssiden vil automatisk sende et
            varsel til PGS-monitor og knappen "Gi tilgang" vil bli tilgjengelig
            i PGS-monitor. Tilgangsstatus i PGS-monitor vil da vises som "Venter
            på tilgang". Øverst i PGS-monitor vil det vises et varsel om at en
            eller flere kandidater venter på tilgang. Du kan da klikke på valget
            "Se hvilke kandidater det gjelder" for å filtrere ut kandidaten det
            er snakk om. Du kan klikke "Nullstill filter" dersom du ønsker å
            fjerne filteret.
          </p>
          <p>
            Som eksamensvakt har du en viktig rolle for å bidra til en sikker og
            rettferdig gjennomføring av eksamen. Du bør påse at kandidaten er
            til stede i eksamenslokalet og er logget på med sin egen bruker i
            PGS før du gir digital tilgang til PGS. Du kan også kontrollere at
            IP-adressen til kandidaten om du ønsker – du kan lese mer om dette i
            kapittelet om innloggingssesjon.
          </p>
          <p>
            I PGS-monitor klikker du på "Gi tilgang". Tilgangsstatus vil da
            endres og en infomelding vil kort vises. Kandidaten sendes
            automatisk videre når eksamensvakten gir tilgang i PGS-monitor.
            Dersom eksamen ikke har startet vil kandidaten få tilgangsstatus
            «Venter på eksamensstart» og komme til ventesiden i kandidatmodulen.
            Dersom eksamen har startet vil kandidaten få status «Har tilgang» og
            komme til oppgavesiden i kandidatmodulen. Dersom kandidaten mot
            formodning ikke blir sendt videre fra autoriseringssiden, kan
            kandidaten oppdatere siden sin i nettleseren.
          </p>
          <p>
            Dagpassord kan benyttes som et alternativ til at det gis tilgang via
            PGS-monitor. Eksamensvakten skal da skrive inn dagspassordet i
            nettleseren til kandidaten. Dagspassordet skal være hemmelig og ikke
            deles med kandidaten. Dagspassord er tilgjengelig for
            skoleadministrator i PAS-eksamen. Merk at autorisering med
            dagspassord er mindre sikkert enn å gi tilgang via PGS-monitor.
          </p>
          <p>
            Det er viktig å merke seg at autoriseringen må gjennomføres hver
            gang kandidaten logger inn på nytt. Det vil si hver gang en ny
            innloggingssesjon starter. Noen eksempler på dette er:
          </p>
          <ul>
            <li>
              Dersom kandidaten logger ut for så å logge på igjen, må hen
              autoriseres på nytt.
            </li>
            <li>
              Dersom kandidaten allerede er autorisert i en nettleser
              (eksempelvis Edge), men ønsker å benytte en annen nettleser
              (eksempelvis Chrome), må kandidaten autoriseres på nytt når hen
              logger inn i PGS med Chrome. Kandidaten vil i så fall miste
              tilgang til PGS via Edge.
            </li>
            <li>
              Dersom kandidaten allerede er autorisert på sin pc, men kandidaten
              må skifte pc pga. tekniske problemer. Kandidaten må da autoriseres
              på nytt når hen logger inn i PGS på den nye pc-en. Kandidaten vil
              i så fall miste tilgang til PGS på den første pc-en.
            </li>
          </ul>
          <p>
            Merk at kandidaten av sikkerhetsmessige årsaker kun kan ha én
            innloggingssesjon med tilgang til PGS. Dette vil si at det er kun
            den siste innloggingssesjonen som gis tilgang som vil ha tilgang til
            PGS. Dersom kandidaten tidligere hadde en annen innloggingssesjon
            med tilgang til PGS vil denne få fjernet autorisering slik som
            beskrevet i eksemplene over.
          </p>
          <p>
            Dersom kandidaten tidligere har fått tilgang vil det stå "Gi tilgang
            på nytt" på knappen for å gi tilgang.
          </p>
          <p>
            Merk at for todelt eksamen kan det kun gis digital tilgang til del
            2. Del 1 for todelt eksamen skal kun gjennomføres på papir, og del 1
            er derfor ikke tilgjengelig i PGS for kandidatene.
          </p>
        </section>

        <section>
          <h2 id="tilgang2">Tilgang</h2>
          <p>
            I kolonnen "Tilgang" vises kandidatens tilgangsstatus i PGS. For at
            kandidaten skal ha tilgang til eksamensoppgaven, og kunne levere
            eksamen digitalt må kandidaten ha digital tilgang til PGS.
          </p>
          <p>For V-2025 vil en kandidat ha en av følgende tilgangsstatuser:</p>

          <table className="table-auto border-collapse border border-gray-300 w-full">
            <thead>
              <tr className="bg-gray-100">
                <th className="border px-4 py-2">Tilgangsstatus</th>
                <th className="border px-4 py-2">Beskrivelse</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="border px-4 py-2 font-semibold">
                  Ikke-dokumentert fravær
                </td>
                <td className="border px-4 py-2">
                  <p>
                    Denne fraværsstatusen vises dersom eksamensvakten har satt
                    "Ikke-dokumentert fravær" i PGS-monitor eller dersom
                    skoleadministrator har satt "Ikke-dokumentert fravær" i
                    PAS-eksamen.
                  </p>
                  <p>
                    Kandidaten vil ikke ha tilgang til PGS. Kandidaten får opp
                    fraværssiden dersom hen logger på.
                  </p>
                </td>
              </tr>
              <tr>
                <td className="border px-4 py-2 font-semibold">
                  Dokumentert fravær
                </td>
                <td className="border px-4 py-2">
                  <p>
                    Denne fraværsstatusen vises dersom eksamensvakten har satt
                    "Dokumentert fravær" i PGS-monitor eller dersom
                    skoleadministrator har satt "Dokumentert fravær" i
                    PAS-eksamen.
                  </p>
                  <p>
                    Kandidaten vil ikke ha tilgang til PGS. Kandidaten får opp
                    fraværssiden dersom hen logger på.
                  </p>
                </td>
              </tr>
              <tr>
                <td className="border px-4 py-2 font-semibold">Ikke tilgang</td>
                <td className="border px-4 py-2">
                  Kandidaten har ikke fått tilgang til PGS. Kandidaten får opp
                  autoriseringssiden dersom hen logger på.
                </td>
              </tr>
              <tr>
                <td className="border px-4 py-2 font-semibold">
                  Venter på tilgang
                </td>
                <td className="border px-4 py-2">
                  Kandidaten er autentisert og har logget inn i PGS og kommet
                  til autoriseringssiden. Autoriseringssiden har automatisk
                  sendt et varsel til PGS-monitor, og knappen "Gi tilgang" er
                  tilgjengelig i PGS-monitor.
                </td>
              </tr>
              <tr>
                <td className="border px-4 py-2 font-semibold">
                  Venter på eksamensstart
                </td>
                <td className="border px-4 py-2">
                  <p>
                    Kandidaten er autorisert av eksamensvakten, men del 2 har
                    ikke startet ennå. For eksamen med én del vises "Venter på
                    eksamensstart" før 09:00.
                  </p>
                  <p>
                    Kandidaten vil ikke ha tilgang på eksamensoppgaven og til å
                    levere digitalt, men vil få automatisk tilgang til dette når
                    eksamen starter.
                  </p>
                </td>
              </tr>
              <tr>
                <td className="border px-4 py-2 font-semibold">Har tilgang</td>
                <td className="border px-4 py-2">
                  Kandidaten er autorisert av eksamensvakten og har tilgang til
                  eksamensoppgaven og til å levere digitalt.
                </td>
              </tr>
              <tr>
                <td className="border px-4 py-2 font-semibold">
                  Digital tilgang sperret
                </td>
                <td className="border px-4 py-2">
                  <p>
                    Eksamensvakten har aktivt valgt å sperre digital tilgang til
                    PGS for kandidaten.
                  </p>
                  <p>
                    Kandidaten vil ikke ha tilgang på eksamensoppgaven og til å
                    levere digitalt. Kandidaten vil komme til ikke-tilgang-siden
                    dersom hen logger på.
                  </p>
                </td>
              </tr>
            </tbody>
          </table>
          <p className="mt-4">
            Merk at for todelt eksamen gjelder tilgangsstatusen kun for del 2.
          </p>
        </section>
        <section>
          <h2 id="Aktivitetslogg">Aktivitetslogg</h2>
          <p>
            Ved klikke på nedtrekkslisten "Flere valg" og deretter{" "}
            <span className="underline">Se logg</span> vises et modal-vindu med
            arkfanene "Aktivitetslogg" og "Innloggingssesjon/IP" for en
            spesifikk kandidat.
          </p>
          <p>
            I arkfanen "Aktivitetslogg" vises tilsvarende aktivitetslogg som i
            den gamle PGS-monitor. Det er en foreløpig en teknisk begrensning i
            PGS på vising av IP-adresser, for IPv6 adresser vises kun de siste
            tolv tegnene i adressen (se skjermbildet på neste side).
          </p>
          <p>
            Merk at også IP-adressen til eksamensvakten vises i aktivitetsloggen
            når en eksamensvakt endrer status for en kandidat. Dette gjelder
            også når eksamensvakten laster opp og leverer filer på vegne av
            kandidaten.
          </p>
        </section>
        <section>
          <h2 id="innlogginssesjon-og-ip">Innloggingssesjon/IP</h2>
          <p>
            Ved klikke på nedtrekkslisten "Flere valg" og deretter{" "}
            <span className="underline">Se logg</span> vises et modal-vindu med
            arkfanene "Aktivitetslogg" og "Innloggingssesjon/IP" for en
            spesifikk kandidat.
          </p>
          <p>
            I arkfanen "Innloggingssesjon/IP" vil du se alle innloggingssesjoner
            som kandidaten har hatt i PGS så langt på eksamensdagen.
          </p>
          <p>
            Når en kandidat logger inn vil PGS registrere kandidatens
            innloggingssesjon og IP-adresse. Alle innloggingssesjoner som
            kandidaten har hatt på eksamensdagen vil vises i arkfanen
            "Innloggingssesjon/IP". Det vises også hvilken innloggingssesjon som
            har tilgang til PGS. Som tidligere beskrevet så er det kun én av
            kandidatens innloggingssesjoner som kan ha tilgang til PGSA.
          </p>
          <p>
            I kolonnen IP vises kandidatens sist aktive IP-adresse registrert av
            PGS.
          </p>
          <p>
            I kolonnen "Aktiv" ser du om en kandidat har en aktiv tilkobling til
            PGS eller ikke. Dersom kandidaten har en aktiv innloggingssesjon
            vises en grønn sirkel og teksten "Aktiv". Dette forutsetter at
            kandidaten er innlogget i PGS og at innloggingssesjonen er
            autorisert av eksamensvakten. Merk at det går flere sekunder mellom
            hver gang PGS sjekker om kandidaten er aktiv. PGS sjekker dette kun
            når kandidaten står på oppgavesiden og leveringssiden. Dersom
            kandidaten lukker nettleseren hvor hen er innlogget, eller setter
            pc-en sin i dvalemodus vil aktivstatus settes til en grå sirkel og
            teksten "Inaktiv". Det kan ta noen minutter før PGS setter
            aktivstatus til "Inaktiv".
          </p>
          <p>
            Dersom du som eksamensvakt ønsker å frata kandidaten tilgang til
            PGS, kan du klikke på knappen "Fjern tilgang". Kandidaten vil da
            miste tilgang til PGS og vil komme til autoriseringssiden dersom hen
            oppdaterer nettleseren sin. Merk da at et nytt varsel automatisk vil
            gå til PGS-monitor og knappen "Gi tilgang" vil vises på nytt. Ved å
            klikke "Fjern tilgang" vil ikke kandidaten bli permanent sperret ute
            slik som for valget "Sperr digital tilgang". Typisk kan valget
            "Fjern tilgang" benyttes dersom eksamensvakten mistenker
            ureglementert bruk av PGS og ønsker å gi tilgang på nytt slik at
            vakten kan følge nøye med på kandidatens pc.
          </p>
          <p>
            Dersom man ekspanderer en innloggingssesjon får man opp alle
            IP-adresser som er logget/registrert på kandidaten for den aktuelle
            innloggingssesjonen. Det vises også info om starttidspunkten for
            hver enkelt økt og siste tidspunkt som PGS registrerte en aktiv
            tilkobling. I tillegg vises info om nettleser, operativsystem og
            enhet. Merk at loggdataene ikke alltid vil være presise for
            nettleser, operativsystem og enhet. For eksempel vises Windows 11
            som "Windows 10 64-bit". Merk at loggdata kun registreres når
            kandidaten benytter en autorisert innloggingssesjon som har en aktiv
            tilkobling til PGS og når kandidaten står på oppgavesiden og
            leveringssiden.
          </p>
        </section>
        <section>
          <h2 id="forskjeller-pa-eksamen-med-en-del-og-todelt-eksamen">
            {" "}
            Forskjeller på eksamen med én del og todelt eksamen
          </h2>
          <p>
            Det er flere forskjeller mellom eksamen med én del og todelt
            eksamen. I denne brukerveiledningen det er derfor laget separate
            beskrivelser for disse i egne kapitler.
          </p>
          <p>
            For todelt eksamen skal del 1 kun gjennomføres på papir. Del 1 er
            derfor ikke tilgjengelig i PGS for kandidatene. Del 1 har derfor
            ikke statuser knyttet til pålogging og autorisasjon for kandidater.
            For todelt eksamen har del 2 en rekke likheter med eksamen med én
            del da begge disse kan gjennomføres digitalt.
          </p>
          <p>
            Når du som eksamensvakt skal levere på vegne av en kandidat må du
            angi eksamensdel for todelt eksamen. Dette er ikke nødvendig for
            eksamen med én del.
          </p>
        </section>
        <section>
          <h2 id="eksamen-med-en-del">Eksamen med én del</h2>
          <p>
            For kandidater som gjennomfører eksamen med én del vil du se
            fremdriftsstatus under kolonnen "Eksamen".
          </p>
          <p>
            Kapittelet “Gi tilgang til kandidater” beskriver hvordan du kan gi
            tilgang til eksamen med én del.
          </p>
          <p>
            Kapittelet “Registrere fravær” beskriver hvordan du registrerer
            kandidater med fravær.
          </p>
          <p>
            Kandidatene vil normalt sett levere besvarelsene digitalt i PGS.
            Dersom en kandidat velger å levere besvarelsen på papir kan skolen
            skanne papirbesvarelsen og levere digitalt på vegne av kandidaten.
            Kandidater som velger å gjennomføre og levere på papir trenger ikke
            å logge inn i PGS.
          </p>
          <p>
            Skolen skal som hovedregel levere eksamen digitalt i PGS.
            Alternativt kan besvarelsen sendes i posten. Da skal hele
            eksamensbesvarelsen sendes i posten. Dette gjør det enklere for
            sensor å vurdere besvarelsen samlet. Dersom skolen velger å sende
            besvarelsen via post, skal dette markeres ved å sette status «Sendes
            i posten».
          </p>
          <p>
            Merk at statusen "Sendes i posten" kun skal benyttes dersom skolen
            velger å sende hele besvarelsen via post. Sensor vil da få info i
            PAS om at besvarelsen er sendt i posten, og eventuelle opplastede
            filer vil <span className="underline">ikke</span> vises for sensor.
          </p>
          <p>
            Eksamensvakt har muligheten til å laste opp og levere på vegne av
            kandidaten, enten via PGS-monitor eller via gruppeopplasting. Det er
            en separat brukerveiledning for
            <a
              className="ml-1 mr-1"
              href="/brukerveiledning/gruppeopplaster"
            >
              gruppeopplasting
            </a>
            hvor du kan lese mer om dette.
          </p>{" "}
          <div>
            <h3 id="statuser-eksamen-med-en-del">
              Statuser – eksamen med én del
            </h3>
            <div>
              <table className="table-auto border-collapse w-full">
                <thead>
                  <tr>
                    <th className="border px-4 py-2">Status</th>
                    <th className="border px-4 py-2">Beskrivelse</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td className="border px-4 py-2">Ikke lastet opp </td>
                    <td className="border px-4 py-2">
                      Det er ikke lastet opp noen filer for kandidaten. Dvs. at
                      verken kandidaten eller eksamensvakten har lastet opp noen
                      filer
                    </td>
                  </tr>

                  <tr>
                    <td className="border px-4 py-2">Laster opp</td>
                    <td className="border px-4 py-2">
                      Kandidaten eller eksamensvakten har lastet opp en eller
                      flere besvarelsesfiler, men har ikke levert.
                    </td>
                  </tr>
                  <tr>
                    <td className="border px-4 py-2">Levert digitalt</td>
                    <td className="border px-4 py-2">
                      Kandidaten har levert besvarelsen digitalt. Statusen
                      settes også dersom eksamensvakten leverer på kandidatens
                      vegne. Digital tilgang er blokkert, og kandidaten får opp
                      kvitteringssiden dersom hen logger på. Kandidaten markeres
                      med grønn bakgrunn i PGS-monitor.
                    </td>
                  </tr>
                  <tr>
                    <td className="border px-4 py-2">Sendes i posten</td>
                    <td className="border px-4 py-2">
                      Kandidaten har sendes i posten. Skolen har valgt å sende
                      besvarelsen i posten. Digital tilgang er blokkert, og
                      kandidaten får opp kvitteringssiden dersom hen logger på.
                      Kandidaten markeres med grønn bakgrunn i PGS-monitor.
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </section>
        <section>
          <h2 id="todelt-eksamen"> Todelt eksamen</h2>
          <p>
            For kandidater som tar eksamen med to deler vil du se én
            fremdriftsstatus pr del i PGS-monitor, under kolonnene "Del 1" og
            "Del 2".
          </p>
          <p>
            Kapittelet “Gi tilgang til kandidater” beskriver hvordan du kan gi
            tilgang for kandidater. Beskrivelsen gjelder også for todelt
            eksamen, men merk at tilgangsstyringen kun gjelder for del 2.
          </p>
          <p>
            Kapittelet “Registrere fravær” beskriver hvordan du registrerer
            kandidater med fravær.
          </p>
          <p>
            For todelt eksamen er del 1 alltid uten digitale hjelpemidler.
            Derfor er det ikke mulig å gi kandidaten tilgang til å levere
            digitalt for denne eksamensdelen. Det er vanlig at skolen skanner
            kandidatens papirbesvarelse for del 1 og leverer denne digitalt i
            PGS. Som eksamensvakt har du mulighet til å levere del 1 for
            kandidaten, enten via "Lever for kandidat" eller via
            gruppeopplasteren.
          </p>
          <p>
            Starttidspunktet for del 2 varier avhengig av fagkode. Del 2 starter
            10:00, 10:45, 11:00 eller 12:00. Kandidaten vil ikke få tilgang på
            eksamensoppgaven for del 2 i PGS før starttidspunktet. Dette gjelder
            selv om kandidaten er innlogget og har fått tilgang i PGS.
          </p>
          <p>
            Kandidatene vil ofte levere besvarelsene for del 2 digitalt i PGS.
            Dersom en kandidat velger å levere besvarelsen på papir kan skolen
            skanne papirbesvarelsen og levere digitalt på vegne av kandidaten.
            Kandidater som velger å levere del 2 på papir trenger ikke å logge
            inn i PGS.
          </p>
          <p>
            Skolen skal som hovedregel levere begge eksamensdelene digitalt i
            PGS. Alternativt kan besvarelsen sendes i posten. Da skal både
            eksamen del én og del to sendes i posten. Dette gjør det enklere for
            sensor å vurdere besvarelsen samlet. Dersom skolen velger å sende
            besvarelsen via post, skal dette markeres ved å sette status «Sendes
            i posten» både for del 1 og del 2.
          </p>
          <p>
            Merk at statusen "Sendes i posten" kun skal benyttes dersom skolen
            velger å sende hele besvarelsen via post. Sensor vil da få info i
            PAS om at besvarelsen er sendt i posten, og eventuelle opplastede
            filer vil ikke vises for sensor.
          </p>
          <p>
            Eksamensvakt har muligheten til å laste opp og levere for
            kandidaten, enten via PGS-monitor eller via gruppeopplasting. Dette
            gjelder både for del 1 og del 2. Det er separate brukerveiledninger
            for
            <a
              className="ml-1 mr-1"
              href="/brukerveiledning/gruppeopplaster"
            >
              gruppeopplasting
            </a>
            og lever for kandidat hvor du kan lese mer om dette.
          </p>
          <div>
            <h3 id="fremdriftsstatuser-todelt-eksamen">
              {" "}
              Fremdriftsstatuser – todelt eksamen
            </h3>
            <p>
              I tabellen under angir kolonnene del 1 og del 2 om en status
              gjelder for de respektive delene.
            </p>
            <div>
              <table className="table-auto border-collapse w-full">
                <thead>
                  <tr>
                    <th className="border px-4 py-2">Status</th>
                    <th className="border px-4 py-2">Del 1</th>
                    <th className="border px-4 py-2">Del 2</th>
                    <th className="border px-4 py-2">Beskrivelse</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td className="border px-4 py-2">Ikke lastet opp</td>
                    <td className="border px-4 py-2">✔</td>
                    <td className="border px-4 py-2">✔</td>
                    <td className="border px-4 py-2">
                      Det er ikke lastet opp noen filer for kandidaten. Dvs. at
                      verken kandidaten eller eksamensvakten har lastet opp noen
                      filer
                    </td>
                  </tr>

                  <tr>
                    <td className="border px-4 py-2">Laster opp</td>
                    <td className="border px-4 py-2">✔</td>
                    <td className="border px-4 py-2">✔</td>
                    <td className="border px-4 py-2">
                      Kandidaten eller eksamensvakten har lastet opp en eller
                      flere besvarelsesfiler, men ikke levert. Status settes
                      hver del for seg.
                    </td>
                  </tr>
                  <tr>
                    <td className="border px-4 py-2">Levert digitalt</td>
                    <td className="border px-4 py-2">✔</td>
                    <td className="border px-4 py-2">✔</td>
                    <td className="border px-4 py-2">
                      Kandidaten har levert besvarelsen digitalt. Statusen
                      settes også dersom eksamensvakten leverer på kandidatens
                      vegne. Dersom statusen er satt for del 2 vil ikke
                      kandidaten ha tilgang til PGS, og kandidaten får opp
                      kvitteringssiden dersom hen logger på. Kandidaten markeres
                      med grønn bakgrunn i PGS-monitor.
                    </td>
                  </tr>
                  <tr>
                    <td className="border px-4 py-2">Sendes i posten</td>
                    <td className="border px-4 py-2">✔</td>
                    <td className="border px-4 py-2">✔</td>
                    <td className="border px-4 py-2">
                      Kandidaten har sendes i posten. Skolen har valgt å sende
                      besvarelsen i posten. Dersom statusen er satt for del 2
                      vil ikke kandidaten ha tilgang til PGS, og kandidaten får
                      opp kvitteringssiden dersom hen logger på. Kandidaten
                      markeres med grønn bakgrunn i PGS-monitor.
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </section>
      </div>
    </>
  );
}
