"use client";

import React, { useState } from "react";
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  getSortedRowModel,
  SortingState,
  useReactTable,
} from "@tanstack/react-table";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import useFileHandler from "@/hooks/useFileHandler";
import { getColumns } from "./columnsSubmitted";
import useFileHandlerForMonitor from "@/hooks/useFilehandlerForMonitor";
import { IUploadedFile } from "@/interface/IUploadedFile";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog";
import { toast } from "@/components/ui/use-toast";

interface DataTableProps<IUploadedFile> {
  columns: ColumnDef<IUploadedFile>[];
  data: IUploadedFile[];
}

export function DataTableSubmitted<TData extends IUploadedFile>({
  data,
}: DataTableProps<TData>) {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [filesToDelete, setFilesToDelete] = useState<Set<string>>(new Set());
  const { deleteFile, setTestPart, deliverFile } = useFileHandlerForMonitor();
  const [fileToDelete, setFileToDelete] = useState<IUploadedFile | null>(null);
  const [dialogOpenSingleFile, setDialogOpenSingleFile] = useState(false);

  async function handleFileDelete(file: IUploadedFile): Promise<void> {
    setFileToDelete(file);
    setDialogOpenSingleFile(true);
  }

  const columns = getColumns({ handleFileDelete, filesToDelete });

  const table = useReactTable({
    data,
    columns: columns as ColumnDef<TData, any>[],
    getCoreRowModel: getCoreRowModel(),
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    state: {
      sorting,
    },
  });

  const selectedRowsCount = table.getSelectedRowModel().rows.length;

  return (
    <div className="bg-green-50 p-4">
      <div className="overflow-x-auto">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id} className="text-left font-bold">
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id} className="text-left">
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  Ingen filer med feil
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      <AlertDialog open={dialogOpenSingleFile}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Du er i ferd med å slette en levert besvarelsesfil </AlertDialogTitle>
            <AlertDialogDescription>
              Er du sikker på at du vil slette filen{" "}
              <span className="font-semibold">{fileToDelete?.Name}</span>?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setDialogOpenSingleFile(false)}>
              Avbryt
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={async () => {
                if (fileToDelete) {
                  setFilesToDelete((prev) =>
                    new Set(prev).add(fileToDelete.FileGuid)
                  );
                  setDialogOpenSingleFile(false);

                  try {
                    await deleteFile(fileToDelete.FileGuid);
                    table.resetRowSelection();
                  } catch (error) {
                    console.error(
                      `Feil ved sletting av fil ${fileToDelete.Name}:`,
                      error
                    );
                    toast({
                      variant: "destructive",
                      title: "Feil ved sletting",
                      description: `Kunne ikke slette filen ${fileToDelete.Name}. Prøv igjen senere.`,
                    });
                  } finally {
                    setFilesToDelete((prev) => {
                      const newSet = new Set(prev);
                      newSet.delete(fileToDelete.FileGuid);
                      return newSet;
                    });
                  }
                }
              }}
            >
              Slett filen
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
